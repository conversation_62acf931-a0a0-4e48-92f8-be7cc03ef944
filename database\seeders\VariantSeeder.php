<?php

namespace Database\Seeders;

use App\Models\Variant;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VariantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        // $variants = [
        //     ['id' => 1, 'inventory_id' => 1,'quantity'=>55,'size'=>'M','color'=>'red'],
        //     ['id' => 2, 'inventory_id' => 2,'quantity'=>20,'size'=>'S','color'=>'Blue'],
        // ];
        // foreach ($variants as $variant) {
        //     Variant::create($variant);
        // }
    }
}
