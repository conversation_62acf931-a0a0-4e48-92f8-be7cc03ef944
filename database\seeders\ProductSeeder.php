<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        // $products = [
        //     ['id' => 1, 'name' => 'zif 7ayati','subcategory_id'=>1,'description'=>'fabouuuuuuuuuuuuuur'],
        //     ['id' => 2, 'name' => 'zif mamati','subcategory_id'=>1,'description'=>'fabouuuuuuuuuuuuuur'],
        // ];
        // foreach ($products as $product) {
        //     Product::create($product);
        // }
    }
}
