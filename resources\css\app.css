@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
    .input-number::-webkit-outer-spin-button,
    .input-number::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .triangle-left {
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-right: 10px solid #fff;
        border-bottom: 5px solid transparent;
    }
    #infoContainer input,
    #infoContainer textarea,
    #infoContainer select {
        border: 1px solid #ccc;
        border-radius: 5px;
    }
}
        

.invisible-scrollbar::-webkit-scrollbar{
    width: 4px;
    background-color: gainsboro;
    border-radius: 6px;
}
.invisible-scrollbar::-webkit-scrollbar-thumb {
    width: 4px;
    background: black;
    border-radius: 10px;
}
