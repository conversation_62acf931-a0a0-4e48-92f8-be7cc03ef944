<?php

namespace Database\Seeders;

use App\Models\Inventory;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class InventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        // $inventories = [
        //     ['id' => 1, 'product_id' => 1,'prePrice'=>33,'postPrice'=>40,'exPrice'=>44,'type'=>'mobra','description'=>'kighawet'],
        //     ['id' => 2, 'product_id' => 2,'prePrice'=>34,'postPrice'=>41,'exPrice'=>45,'type'=>'7rir','description'=>'kighawet ichfak cherir'],
        // ];
        // foreach ($inventories as $inventory) {
        //     Inventory::create($inventory);
        // }
    }
}
